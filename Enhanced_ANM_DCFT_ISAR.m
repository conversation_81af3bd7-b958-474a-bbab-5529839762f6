%-------------------------------------------------------------------------%
%--------     Enhanced ISAR Imaging using Atomic Norm Minimization     -%
%--------          Based on DCFT with Joint Phase Correction           -%
%--------                    Enhanced Version                           -%
%-------------------------------------------------------------------------%

function Enhanced_ANM_DCFT_ISAR()
clc; clear all; close all;

% 添加CVX路径（如果已安装）
% addpath('path/to/cvx');

% 从原始代码加载船舶目标数据
[s_r_tm2, tm2, r, PRF, fc, B, c, Pos] = load_ship_data();

[Num_r, Num_tm] = size(s_r_tm2);
delta_r = c/(2*B);

% ANM-DCFT 参数设置
params = struct();
params.lambda = 0.1;        % 稀疏正则化参数
params.mu = 0.01;           % 平滑正则化参数
params.max_iter = 20;       % 最大迭代次数
params.tol = 1e-6;          % 收敛阈值
params.L_windows = 10;      % 频率窗口数量
params.F_max = PRF/2;       % 最大频率

% 目标距离单元范围
target_range = 30:56;

% 执行ANM-DCFT成像
fprintf('开始ANM-DCFT成像处理...\n');
tic;
[R_D_ANM, alpha_est, beta_est, convergence_info] = ANM_DCFT_imaging(s_r_tm2, tm2, target_range, params);
processing_time = toc;
fprintf('ANM-DCFT处理完成，耗时: %.2f 秒\n', processing_time);

% 对比原始DCFT结果
fprintf('计算原始DCFT结果用于对比...\n');
R_D_DCFT_original = original_DCFT_imaging(s_r_tm2, tm2, target_range);

% 结果显示和分析
display_results(R_D_ANM, R_D_DCFT_original, alpha_est, beta_est, convergence_info, target_range);

end

%% ANM-DCFT主要成像函数
function [R_D_ANM, alpha_est, beta_est, convergence_info] = ANM_DCFT_imaging(s_r_tm, tm, target_range, params)
    
    [Num_r, Num_tm] = size(s_r_tm);
    R = length(target_range);
    
    % 初始化变量
    alpha_est = zeros(R, 1);
    beta_est = zeros(R, 1);
    x_r = cell(R, 1);
    
    % 使用原始DCFT结果作为初始化
    fprintf('初始化相位参数...\n');
    [alpha_init, beta_init] = initialize_parameters(s_r_tm, tm, target_range);
    alpha_est = alpha_init;
    beta_est = beta_init;
    
    % 初始化频谱估计
    for idx = 1:R
        r_idx = target_range(idx);
        x_r{idx} = fft(s_r_tm(r_idx, :), params.L_windows * Num_tm);
    end
    
    % 收敛信息记录
    convergence_info = struct();
    convergence_info.cost = zeros(params.max_iter, 1);
    convergence_info.alpha_change = zeros(params.max_iter, 1);
    convergence_info.beta_change = zeros(params.max_iter, 1);
    
    % 交替优化迭代
    fprintf('开始交替优化迭代...\n');
    for iter = 1:params.max_iter
        fprintf('迭代 %d/%d\n', iter, params.max_iter);
        
        % 保存上一次迭代的参数
        alpha_prev = alpha_est;
        beta_prev = beta_est;
        
        % Step 1: 固定相位参数，优化频谱 (使用近似方法替代完整SDP)
        x_r = optimize_spectrum_approximate(s_r_tm, tm, target_range, alpha_est, beta_est, params);
        
        % Step 2: 固定频谱，优化相位参数
        [alpha_est, beta_est] = optimize_phase_parameters(s_r_tm, tm, target_range, x_r, alpha_est, beta_est, params);
        
        % 计算目标函数值和收敛性
        cost = compute_cost_function(s_r_tm, tm, target_range, x_r, alpha_est, beta_est, params);
        convergence_info.cost(iter) = cost;
        convergence_info.alpha_change(iter) = norm(alpha_est - alpha_prev);
        convergence_info.beta_change(iter) = norm(beta_est - beta_prev);
        
        % 检查收敛性
        if iter > 1 && (convergence_info.alpha_change(iter) < params.tol && ...
                       convergence_info.beta_change(iter) < params.tol)
            fprintf('算法在第 %d 次迭代收敛\n', iter);
            break;
        end
    end
    
    % 构建最终成像结果
    R_D_ANM = zeros(Num_r, Num_tm);
    for idx = 1:R
        r_idx = target_range(idx);
        % 取频谱的中心部分作为成像结果
        spectrum_length = length(x_r{idx});
        center_start = round(spectrum_length/2 - Num_tm/2) + 1;
        center_end = center_start + Num_tm - 1;
        R_D_ANM(r_idx, :) = abs(x_r{idx}(center_start:center_end));
    end
    
end

%% 近似频谱优化 (使用FISTA替代完整SDP)
function x_r = optimize_spectrum_approximate(s_r_tm, tm, target_range, alpha_est, beta_est, params)
    
    R = length(target_range);
    [~, Num_tm] = size(s_r_tm);
    x_r = cell(R, 1);
    
    % 对每个距离单元分别优化
    for idx = 1:R
        r_idx = target_range(idx);
        s_obs = s_r_tm(r_idx, :).';
        
        % 构建观测矩阵
        A_r = construct_observation_matrix(tm, alpha_est(idx), beta_est(idx), params.L_windows * Num_tm);
        
        % 使用FISTA算法求解L1正则化问题
        x_r{idx} = fista_l1_solver(A_r, s_obs, params.lambda, 100);
    end
    
end

%% FISTA算法求解L1正则化问题
function x = fista_l1_solver(A, b, lambda, max_iter)
    
    [m, n] = size(A);
    x = zeros(n, 1);
    y = x;
    t = 1;
    
    % 计算Lipschitz常数
    L = norm(A' * A, 2);
    step_size = 1 / L;
    
    for iter = 1:max_iter
        x_prev = x;
        
        % 梯度步
        grad = A' * (A * y - b);
        z = y - step_size * grad;
        
        % 软阈值操作
        x = soft_threshold(z, lambda * step_size);
        
        % 更新动量项
        t_prev = t;
        t = (1 + sqrt(1 + 4 * t^2)) / 2;
        y = x + ((t_prev - 1) / t) * (x - x_prev);
        
        % 检查收敛性
        if norm(x - x_prev) / norm(x_prev) < 1e-6
            break;
        end
    end
    
end

%% 软阈值函数
function x = soft_threshold(y, tau)
    x = sign(y) .* max(abs(y) - tau, 0);
end

%% 构建观测矩阵
function A = construct_observation_matrix(tm, alpha, beta, N)
    
    M = length(tm);
    A = zeros(M, N);
    
    % 频率网格
    f_grid = linspace(-0.5, 0.5, N);
    
    for n = 1:N
        for m = 1:M
            phase = 2*pi * (f_grid(n) * tm(m) + 0.5 * alpha * tm(m)^2 + (1/6) * beta * tm(m)^3);
            A(m, n) = exp(-1j * phase);
        end
    end
    
end

%% 优化相位参数
function [alpha_est, beta_est] = optimize_phase_parameters(s_r_tm, tm, target_range, x_r, alpha_init, beta_init, params)
    
    R = length(target_range);
    
    % 定义目标函数
    objective = @(theta) phase_objective_function(theta, s_r_tm, tm, target_range, x_r, params);
    
    % 初始参数向量
    theta_init = [alpha_init; beta_init];
    
    % 使用优化算法
    options = optimoptions('fminunc', 'Display', 'off', 'Algorithm', 'quasi-newton');
    theta_opt = fminunc(objective, theta_init, options);
    
    % 提取优化后的参数
    alpha_est = theta_opt(1:R);
    beta_est = theta_opt(R+1:2*R);
    
end

%% 相位参数目标函数
function [cost, grad] = phase_objective_function(theta, s_r_tm, tm, target_range, x_r, params)
    
    R = length(target_range);
    alpha = theta(1:R);
    beta = theta(R+1:2*R);
    
    cost = 0;
    grad = zeros(2*R, 1);
    
    % 数据拟合项
    for idx = 1:R
        r_idx = target_range(idx);
        s_obs = s_r_tm(r_idx, :).';
        
        % 构建观测矩阵
        A_r = construct_observation_matrix(tm, alpha(idx), beta(idx), length(x_r{idx}));
        
        % 预测信号
        s_pred = A_r * x_r{idx};
        
        % 数据拟合误差
        residual = s_obs - s_pred;
        cost = cost + norm(residual)^2;
        
        % 计算梯度 (数值梯度近似)
        h = 1e-8;
        
        % alpha梯度
        A_r_alpha = construct_observation_matrix(tm, alpha(idx) + h, beta(idx), length(x_r{idx}));
        s_pred_alpha = A_r_alpha * x_r{idx};
        grad_alpha = -2 * real((s_obs - s_pred_alpha)' * (s_pred_alpha - s_pred)) / h;
        grad(idx) = grad_alpha;
        
        % beta梯度
        A_r_beta = construct_observation_matrix(tm, alpha(idx), beta(idx) + h, length(x_r{idx}));
        s_pred_beta = A_r_beta * x_r{idx};
        grad_beta = -2 * real((s_obs - s_pred_beta)' * (s_pred_beta - s_pred)) / h;
        grad(R + idx) = grad_beta;
    end
    
    % 添加平滑正则项
    if R > 1
        % alpha平滑项
        alpha_diff = diff(alpha);
        cost = cost + params.mu * norm(alpha_diff)^2;
        grad(1:R-1) = grad(1:R-1) + 2 * params.mu * alpha_diff;
        grad(2:R) = grad(2:R) - 2 * params.mu * alpha_diff;
        
        % beta平滑项
        beta_diff = diff(beta);
        cost = cost + params.mu * norm(beta_diff)^2;
        grad(R+1:2*R-1) = grad(R+1:2*R-1) + 2 * params.mu * beta_diff;
        grad(R+2:2*R) = grad(R+2:2*R) - 2 * params.mu * beta_diff;
    end
    
end

%% 计算目标函数值
function cost = compute_cost_function(s_r_tm, tm, target_range, x_r, alpha_est, beta_est, params)
    
    R = length(target_range);
    cost = 0;
    
    % 数据拟合项
    for idx = 1:R
        r_idx = target_range(idx);
        s_obs = s_r_tm(r_idx, :).';
        A_r = construct_observation_matrix(tm, alpha_est(idx), beta_est(idx), length(x_r{idx}));
        s_pred = A_r * x_r{idx};
        cost = cost + norm(s_obs - s_pred)^2;
        
        % 稀疏项 (L1范数近似)
        cost = cost + params.lambda * norm(x_r{idx}, 1);
    end
    
    % 平滑项
    if R > 1
        cost = cost + params.mu * (norm(diff(alpha_est))^2 + norm(diff(beta_est))^2);
    end
    
end

%% 参数初始化
function [alpha_init, beta_init] = initialize_parameters(s_r_tm, tm, target_range)
    
    R = length(target_range);
    alpha_init = zeros(R, 1);
    beta_init = zeros(R, 1);
    
    % 使用简化的DCFT搜索进行初始化
    delta_alpha = 8;
    alpha_search = -20:delta_alpha:320;
    delta_beta = 100;
    beta_search = -500:delta_beta:2400;
    
    for idx = 1:R
        r_idx = target_range(idx);
        s_tm = s_r_tm(r_idx, :);
        
        max_energy = 0;
        best_alpha = 0;
        best_beta = 0;
        
        for alpha = alpha_search
            for beta = beta_search
                % 相位补偿
                phase_comp = exp(-1j * 2*pi * (0.5*alpha*tm.^2 + (1/6)*beta*tm.^3));
                s_comp = s_tm .* phase_comp;
                
                % FFT能量
                S_fft = fft(s_comp);
                energy = max(abs(S_fft));
                
                if energy > max_energy
                    max_energy = energy;
                    best_alpha = alpha;
                    best_beta = beta;
                end
            end
        end
        
        alpha_init(idx) = best_alpha;
        beta_init(idx) = best_beta;
    end
    
end

%% 原始DCFT成像 (参考函数)
function R_D_DCFT = original_DCFT_imaging(s_r_tm, tm, target_range)
    
    [Num_r, Num_tm] = size(s_r_tm);
    R_D_DCFT = zeros(Num_r, Num_tm);
    
    % DCFT参数设置
    delta_alpha = 8;
    alpha_sea = -16:delta_alpha:320;
    delta_beta = 100;
    beta_sea = -500:delta_beta:2400;
    
    for r_idx = target_range
        s_tm_temp = s_r_tm(r_idx, :);
        S_f = zeros(1, Num_tm);
        
        % DCFT搜索
        for alpha = alpha_sea
            for beta = beta_sea
                phase_comp = exp(-1j * 2*pi * (0.5*alpha*tm.^2 + (1/6)*beta*tm.^3));
                s_dechirp = s_tm_temp .* phase_comp;
                S_dechirp = fft(s_dechirp);
                
                for n = 1:Num_tm
                    S_f(n) = S_f(n) + S_dechirp(n);
                end
            end
        end
        
        R_D_DCFT(r_idx, :) = abs(S_f);
    end
    
end

%% 加载船舶数据 (从原始代码提取)
function [s_r_tm2, tm2, r, PRF, fc, B, c, Pos] = load_ship_data()
    
    % 船舶模型散射点位置
    Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...
           0 -1 0;...
           1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...
           -9.5 0.2 0.5;...
           -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...
           0 1 0;...
           1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...
           10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;...
           9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;...
           5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...
           5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;...
           0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;...
           -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;...
           -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5];
    
    % 雷达参数
    B = 80*1e6;
    c = 3*1e8;
    PRF = 1400;
    fc = 5.2*1e9;
    delta_r = c/(2*B);
    r = -50*delta_r:delta_r:50*delta_r;
    tm2 = 0:(1/PRF):0.501;
    
    % 生成回波数据 (简化版本)
    [s_r_tm2] = generate_ship_echo(Pos, r, tm2, B, c, PRF, fc);
    
end

%% 生成船舶回波数据
function s_r_tm2 = generate_ship_echo(Pos, r, tm2, B, c, PRF, fc)
    
    % 坐标变换
    x_Pos = Pos(:,1)*5;
    y_Pos = Pos(:,2)*5;
    z_Pos = Pos(:,3)*5;
    
    % 雷达视线方向
    R = [cos(3*pi/8)*cos(0), cos(3*pi/8)*sin(0), sin(3*pi/8)];
    
    % 运动参数
    x_oumiga = 0.05; y_oumiga = 0.2; z_oumiga = 0.05;
    x_lamda = 0.05; y_lamda = 0.1; z_lamda = 0.05;
    x_gamma = 0.05; y_gamma = 0.4; z_gamma = 0.05;
    
    Num_point = size(Pos, 1);
    Num_r = length(r);
    Num_tm = length(tm2);
    
    s_r_tm2 = zeros(Num_r, Num_tm);
    
    for n_point = 1:Num_point
        % 计算径向运动参数
        x_r = y_Pos(n_point)*R(3) - z_Pos(n_point)*R(2);
        y_r = z_Pos(n_point)*R(1) - x_Pos(n_point)*R(3);
        z_r = x_Pos(n_point)*R(2) - y_Pos(n_point)*R(1);
        
        f = x_r*x_oumiga + y_r*y_oumiga + z_r*z_oumiga;
        alpha = x_r*x_lamda + y_r*y_lamda + z_r*z_lamda;
        beita = x_r*x_gamma + y_r*y_gamma + z_r*z_gamma;
        
        % 初始距离
        Delta_R0 = x_Pos(n_point)*R(1) + y_Pos(n_point)*R(2) + z_Pos(n_point)*R(3);
        
        % 距离历程
        Delta_R = f.*tm2 + 0.5*alpha.*tm2.^2 + (1/6)*beita.*tm2.^3 + Delta_R0;
        
        % 相位历程
        sita_Delta_R = 4*pi*(fc/c) * (f.*tm2 + 0.5*alpha.*tm2.^2 + (1/6)*beita.*tm2.^3 + Delta_R0);
        
        % 回波信号
        ones_r = ones(Num_r, 1);
        ones_tm = ones(1, Num_tm);
        Delta_R_matrix = ones_r * Delta_R;
        r_matrix = r.' * ones_tm;
        
        sinc_term = sinc((2*B/c) * (r_matrix - Delta_R_matrix));
        phase_term = exp(1j * (ones_r * sita_Delta_R));
        
        s_r_tm2 = s_r_tm2 + sinc_term .* phase_term;
    end
    
    % 添加相位补偿
    phase_comp = exp(1j * 2*pi * (190.*tm2 + 0.5*40.*tm2.^2 + (1/6)*400.*tm2.^3));
    s_r_tm2 = s_r_tm2 .* repmat(phase_comp, Num_r, 1);
    
end

%% 结果显示和分析
function display_results(R_D_ANM, R_D_DCFT, alpha_est, beta_est, convergence_info, target_range)
    
    % 图像对比显示
    figure('Position', [100, 100, 1200, 800]);
    
    % ANM-DCFT结果
    subplot(2,3,1);
    G_ANM = 20*log10(abs(R_D_ANM)./max(abs(R_D_ANM(:))));
    imagesc(G_ANM); caxis([-30, 0]);
    colorbar; colormap jet;
    title('ANM-DCFT成像结果');
    xlabel('方位'); ylabel('距离');
    
    % 原始DCFT结果
    subplot(2,3,2);
    G_DCFT = 20*log10(abs(R_D_DCFT)./max(abs(R_D_DCFT(:))));
    imagesc(G_DCFT); caxis([-30, 0]);
    colorbar; colormap jet;
    title('原始DCFT成像结果');
    xlabel('方位'); ylabel('距离');
    
    % 差异图
    subplot(2,3,3);
    diff_img = G_ANM - G_DCFT;
    imagesc(diff_img); caxis([-10, 10]);
    colorbar; colormap jet;
    title('成像质量改进 (ANM - DCFT)');
    xlabel('方位'); ylabel('距离');
    
    % 估计的运动参数
    subplot(2,3,4);
    plot(target_range, alpha_est, 'b-o', 'LineWidth', 2);
    title('估计的加速度参数 α');
    xlabel('距离单元'); ylabel('α');
    grid on;
    
    subplot(2,3,5);
    plot(target_range, beta_est, 'r-s', 'LineWidth', 2);
    title('估计的加加速度参数 β');
    xlabel('距离单元'); ylabel('β');
    grid on;
    
    % 收敛曲线
    subplot(2,3,6);
    iter_range = 1:length(convergence_info.cost);
    semilogy(iter_range, convergence_info.cost(iter_range), 'g-', 'LineWidth', 2);
    title('目标函数收敛曲线');
    xlabel('迭代次数'); ylabel('目标函数值');
    grid on;
    
    % 计算性能指标
    fprintf('\n========== 成像质量分析 ==========\n');
    
    % 图像熵对比
    entropy_ANM = EntropyImage(abs(R_D_ANM) + eps);
    entropy_DCFT = EntropyImage(abs(R_D_DCFT) + eps);
    fprintf('图像熵 - ANM: %.4f, DCFT: %.4f\n', entropy_ANM, entropy_DCFT);
    
    % 图像对比度
    contrast_ANM = contrast(abs(R_D_ANM) + eps);
    contrast_DCFT = contrast(abs(R_D_DCFT) + eps);
    fprintf('图像对比度 - ANM: %.4f, DCFT: %.4f\n', contrast_ANM, contrast_DCFT);
    
    % 主瓣宽度分析（选择强散射点）
    [~, max_idx] = max(abs(R_D_ANM(:)));
    [r_max, c_max] = ind2sub(size(R_D_ANM), max_idx);
    
    % 方位向主瓣宽度
    azimuth_profile_ANM = abs(R_D_ANM(r_max, :));
    azimuth_profile_DCFT = abs(R_D_DCFT(r_max, :));
    
    mainlobe_width_ANM = compute_mainlobe_width(azimuth_profile_ANM);
    mainlobe_width_DCFT = compute_mainlobe_width(azimuth_profile_DCFT);
    
    fprintf('方位向主瓣宽度 - ANM: %.2f, DCFT: %.2f\n', mainlobe_width_ANM, mainlobe_width_DCFT);
    fprintf('分辨率改进: %.1f%%\n', (mainlobe_width_DCFT - mainlobe_width_ANM)/mainlobe_width_DCFT * 100);
    
    % 显示算法参数
    fprintf('\n========== 算法参数 ==========\n');
    fprintf('稀疏正则化参数 λ: %.3f\n', 0.1);
    fprintf('平滑正则化参数 μ: %.3f\n', 0.01);
    fprintf('收敛迭代次数: %d\n', length(convergence_info.cost));
    
end

%% 计算主瓣宽度
function width = compute_mainlobe_width(profile)
    
    [max_val, max_idx] = max(profile);
    half_max = max_val / 2;
    
    % 寻找半功率点
    left_idx = max_idx;
    while left_idx > 1 && profile(left_idx) > half_max
        left_idx = left_idx - 1;
    end
    
    right_idx = max_idx;
    while right_idx < length(profile) && profile(right_idx) > half_max
        right_idx = right_idx + 1;
    end
    
    width = right_idx - left_idx;
    
end

%% 图像熵计算
function entropy = EntropyImage(img)
    
    img_norm = img / sum(img(:));
    img_norm = img_norm(img_norm > 0);
    entropy = -sum(img_norm(:) .* log(img_norm(:)));
    
end

%% 对比度计算
function c = contrast(img)
    
    img_norm = img / max(img(:));
    mean_val = mean(img_norm(:));
    c = std(img_norm(:)) / mean_val;
    
end 