%-------------------------------------------------------------------------%
%--------          Enhanced ANM-DCFT Algorithm Test Script            -%
%-------------------------------------------------------------------------%

clc; clear all; close all;

fprintf('========== 增强ANM-DCFT算法测试 ==========\n\n');

% 运行增强的ANM-DCFT算法
fprintf('正在运行增强的ANM-DCFT算法...\n');
try
    Enhanced_ANM_DCFT_ISAR();
    fprintf('ANM-DCFT算法运行成功！\n');
catch ME
    fprintf('运行出错: %s\n', ME.message);
    fprintf('正在运行简化版本进行测试...\n');
    run_simplified_test();
end

%% 简化测试函数
function run_simplified_test()
    
    fprintf('\n========== 运行简化版ANM-DCFT测试 ==========\n');
    
    % 生成测试数据
    [s_r_tm2, tm2, target_range] = generate_test_data();
    
    % 设置参数
    params = struct();
    params.lambda = 0.1;      % 稀疏正则化参数
    params.mu = 0.01;         % 平滑正则化参数
    params.max_iter = 15;     % 最大迭代次数
    params.tol = 1e-6;        % 收敛阈值
    
    % 执行ANM-DCFT成像
    fprintf('开始ANM-DCFT成像处理...\n');
    tic;
    [R_D_ANM, alpha_est, beta_est] = simplified_ANM_DCFT(s_r_tm2, tm2, target_range, params);
    processing_time = toc;
    fprintf('ANM-DCFT处理完成，耗时: %.2f 秒\n', processing_time);
    
    % 对比原始DCFT
    fprintf('计算原始DCFT结果用于对比...\n');
    R_D_DCFT = simplified_DCFT(s_r_tm2, tm2, target_range);
    
    % 显示结果
    display_comparison_results(R_D_ANM, R_D_DCFT, alpha_est, beta_est, target_range);
    
end

%% 生成测试数据
function [s_r_tm2, tm2, target_range] = generate_test_data()
    
    % 雷达参数
    B = 80*1e6;  % 带宽
    c = 3*1e8;   % 光速
    PRF = 1400;  % 脉冲重复频率
    fc = 5.2*1e9; % 载频
    
    % 时间轴
    tm2 = 0:(1/PRF):0.3;  % 缩短观测时间以加速测试
    Num_tm = length(tm2);
    
    % 距离轴
    delta_r = c/(2*B);
    r = -50*delta_r:delta_r:50*delta_r;
    Num_r = length(r);
    
    % 目标距离单元
    target_range = 35:50;
    
    % 简化的散射点模型
    scatterer_pos = [
        0, 0, 0;      % 中心散射点
        -5, 0, 0;     % 左侧散射点
        5, 0, 0;      % 右侧散射点
        0, -3, 0;     % 前方散射点
        0, 3, 0;      % 后方散射点
        -3, -2, 1;    % 左前角散射点
        3, 2, 1;      % 右后角散射点
    ];
    
    % 雷达视线方向
    R_los = [cos(pi/4), sin(pi/4), 0];  % 45度视角
    
    % 运动参数
    omega = [0.1, 0.05, 0.02];     % 角速度
    alpha_motion = [20, 40, 10];   % 角加速度
    beta_motion = [200, 400, 100]; % 角加加速度
    
    % 初始化回波矩阵
    s_r_tm2 = zeros(Num_r, Num_tm);
    
    % 生成每个散射点的回波
    for n_scatterer = 1:size(scatterer_pos, 1)
        pos = scatterer_pos(n_scatterer, :) * 5;  % 缩放散射点位置
        
        % 计算径向距离变化
        r_radial = dot(pos, R_los);
        
        % 简化的运动模型
        f_doppler = omega(1);
        alpha_param = alpha_motion(1) + 5*randn();  % 添加少量随机性
        beta_param = beta_motion(1) + 50*randn();
        
        % 距离历程
        R_history = r_radial + f_doppler.*tm2 + 0.5*alpha_param.*tm2.^2 + (1/6)*beta_param.*tm2.^3;
        
        % 相位历程
        phase_history = 4*pi*fc/c * R_history;
        
        % 回波信号
        for n_r = 1:Num_r
            for n_tm = 1:Num_tm
                range_diff = r(n_r) - R_history(n_tm);
                amplitude = sinc(2*B/c * range_diff);
                s_r_tm2(n_r, n_tm) = s_r_tm2(n_r, n_tm) + amplitude * exp(1j * phase_history(n_tm));
            end
        end
    end
    
    % 添加基本的相位补偿
    global_phase = exp(1j * 2*pi * (100.*tm2 + 0.5*30.*tm2.^2 + (1/6)*300.*tm2.^3));
    s_r_tm2 = s_r_tm2 .* repmat(global_phase, Num_r, 1);
    
    % 添加噪声
    noise_power = 0.1;
    noise = noise_power * (randn(Num_r, Num_tm) + 1j*randn(Num_r, Num_tm));
    s_r_tm2 = s_r_tm2 + noise;
    
    fprintf('测试数据生成完成\n');
    fprintf('数据尺寸: %d x %d\n', Num_r, Num_tm);
    fprintf('目标距离单元: %d 到 %d\n', target_range(1), target_range(end));
    
end

%% 简化的ANM-DCFT算法
function [R_D_ANM, alpha_est, beta_est] = simplified_ANM_DCFT(s_r_tm, tm, target_range, params)
    
    [Num_r, Num_tm] = size(s_r_tm);
    R = length(target_range);
    
    % 初始化
    alpha_est = 40 * ones(R, 1);   % 初始加速度参数
    beta_est = 400 * ones(R, 1);   % 初始加加速度参数
    
    % 迭代优化
    for iter = 1:params.max_iter
        fprintf('  迭代 %d/%d\n', iter, params.max_iter);
        
        alpha_prev = alpha_est;
        beta_prev = beta_est;
        
        % 对每个距离单元进行优化
        for idx = 1:R
            r_idx = target_range(idx);
            s_obs = s_r_tm(r_idx, :);
            
            % 搜索最优相位参数 (简化搜索)
            best_alpha = alpha_est(idx);
            best_beta = beta_est(idx);
            max_energy = 0;
            
            alpha_search = alpha_est(idx) + (-20:5:20);
            beta_search = beta_est(idx) + (-100:25:100);
            
            for alpha_test = alpha_search
                for beta_test = beta_search
                    % 相位补偿
                    phase_comp = exp(-1j * 2*pi * (0.5*alpha_test*tm.^2 + (1/6)*beta_test*tm.^3));
                    s_comp = s_obs .* phase_comp;
                    
                    % FFT并计算能量
                    S_fft = fft(s_comp);
                    energy = max(abs(S_fft));
                    
                    % 添加稀疏性惩罚
                    sparsity_penalty = params.lambda * sum(abs(S_fft) > 0.1*max(abs(S_fft)));
                    total_score = energy - sparsity_penalty;
                    
                    if total_score > max_energy
                        max_energy = total_score;
                        best_alpha = alpha_test;
                        best_beta = beta_test;
                    end
                end
            end
            
            alpha_est(idx) = best_alpha;
            beta_est(idx) = best_beta;
        end
        
        % 应用平滑约束
        if params.mu > 0 && R > 2
            alpha_smooth = alpha_est;
            beta_smooth = beta_est;
            
            for idx = 2:R-1
                alpha_smooth(idx) = (1-2*params.mu)*alpha_est(idx) + ...
                                   params.mu*(alpha_est(idx-1) + alpha_est(idx+1));
                beta_smooth(idx) = (1-2*params.mu)*beta_est(idx) + ...
                                  params.mu*(beta_est(idx-1) + beta_est(idx+1));
            end
            
            alpha_est = alpha_smooth;
            beta_est = beta_smooth;
        end
        
        % 检查收敛性
        alpha_change = norm(alpha_est - alpha_prev);
        beta_change = norm(beta_est - beta_prev);
        
        if alpha_change < params.tol && beta_change < params.tol
            fprintf('  算法在第 %d 次迭代收敛\n', iter);
            break;
        end
    end
    
    % 生成最终成像结果
    R_D_ANM = zeros(Num_r, Num_tm);
    
    for idx = 1:R
        r_idx = target_range(idx);
        s_obs = s_r_tm(r_idx, :);
        
        % 使用最优参数进行相位补偿
        phase_comp = exp(-1j * 2*pi * (0.5*alpha_est(idx)*tm.^2 + (1/6)*beta_est(idx)*tm.^3));
        s_comp = s_obs .* phase_comp;
        
        % FFT成像
        S_fft = fft(s_comp, Num_tm);
        
        % 应用软阈值 (稀疏性约束)
        threshold = params.lambda * max(abs(S_fft));
        S_fft_sparse = S_fft .* (abs(S_fft) > threshold);
        
        R_D_ANM(r_idx, :) = abs(S_fft_sparse);
    end
    
end

%% 简化的DCFT算法 (用于对比)
function R_D_DCFT = simplified_DCFT(s_r_tm, tm, target_range)
    
    [Num_r, Num_tm] = size(s_r_tm);
    R_D_DCFT = zeros(Num_r, Num_tm);
    
    % DCFT参数搜索范围
    alpha_search = 0:10:100;
    beta_search = 0:100:800;
    
    for r_idx = target_range
        s_obs = s_r_tm(r_idx, :);
        S_sum = zeros(1, Num_tm);
        
        % 搜索所有参数组合
        for alpha = alpha_search
            for beta = beta_search
                phase_comp = exp(-1j * 2*pi * (0.5*alpha*tm.^2 + (1/6)*beta*tm.^3));
                s_comp = s_obs .* phase_comp;
                S_fft = fft(s_comp);
                S_sum = S_sum + S_fft;
            end
        end
        
        R_D_DCFT(r_idx, :) = abs(S_sum);
    end
    
end

%% 显示对比结果
function display_comparison_results(R_D_ANM, R_D_DCFT, alpha_est, beta_est, target_range)
    
    figure('Position', [100, 100, 1400, 900]);
    
    % ANM-DCFT结果
    subplot(2,3,1);
    G_ANM = 20*log10(abs(R_D_ANM)./max(abs(R_D_ANM(:))));
    imagesc(G_ANM); caxis([-40, 0]);
    colorbar; colormap jet;
    title('ANM-DCFT成像结果');
    xlabel('方位单元'); ylabel('距离单元');
    
    % 原始DCFT结果
    subplot(2,3,2);
    G_DCFT = 20*log10(abs(R_D_DCFT)./max(abs(R_D_DCFT(:))));
    imagesc(G_DCFT); caxis([-40, 0]);
    colorbar; colormap jet;
    title('原始DCFT成像结果');
    xlabel('方位单元'); ylabel('距离单元');
    
    % 改进差异
    subplot(2,3,3);
    diff_img = G_ANM - G_DCFT;
    imagesc(diff_img); caxis([-15, 15]);
    colorbar; colormap jet;
    title('成像质量改进 (ANM - DCFT)');
    xlabel('方位单元'); ylabel('距离单元');
    
    % 估计的运动参数
    subplot(2,3,4);
    plot(target_range, alpha_est, 'b-o', 'LineWidth', 2, 'MarkerSize', 6);
    title('估计的加速度参数 α');
    xlabel('距离单元'); ylabel('α');
    grid on;
    
    subplot(2,3,5);
    plot(target_range, beta_est, 'r-s', 'LineWidth', 2, 'MarkerSize', 6);
    title('估计的加加速度参数 β');
    xlabel('距离单元'); ylabel('β');
    grid on;
    
    % 性能对比
    subplot(2,3,6);
    
    % 计算图像熵
    entropy_ANM = compute_entropy(abs(R_D_ANM) + eps);
    entropy_DCFT = compute_entropy(abs(R_D_DCFT) + eps);
    
    % 计算图像对比度
    contrast_ANM = compute_contrast(abs(R_D_ANM));
    contrast_DCFT = compute_contrast(abs(R_D_DCFT));
    
    % 计算峰值旁瓣比
    pslr_ANM = compute_pslr(R_D_ANM, target_range);
    pslr_DCFT = compute_pslr(R_D_DCFT, target_range);
    
    metrics = {'图像熵', '对比度', 'PSLR (dB)'};
    anm_values = [entropy_ANM, contrast_ANM, pslr_ANM];
    dcft_values = [entropy_DCFT, contrast_DCFT, pslr_DCFT];
    
    x = 1:length(metrics);
    width = 0.35;
    
    bar(x - width/2, anm_values, width, 'FaceColor', 'blue', 'DisplayName', 'ANM-DCFT');
    hold on;
    bar(x + width/2, dcft_values, width, 'FaceColor', 'red', 'DisplayName', 'DCFT');
    
    set(gca, 'XTick', x, 'XTickLabel', metrics);
    ylabel('性能指标值');
    title('算法性能对比');
    legend('Location', 'best');
    grid on;
    
    % 打印数值结果
    fprintf('\n========== 成像质量分析 ==========\n');
    fprintf('图像熵      - ANM: %.4f, DCFT: %.4f (改进: %.1f%%)\n', ...
            entropy_ANM, entropy_DCFT, (entropy_DCFT-entropy_ANM)/entropy_DCFT*100);
    fprintf('图像对比度  - ANM: %.4f, DCFT: %.4f (改进: %.1f%%)\n', ...
            contrast_ANM, contrast_DCFT, (contrast_ANM-contrast_DCFT)/contrast_DCFT*100);
    fprintf('峰值旁瓣比 - ANM: %.2f dB, DCFT: %.2f dB (改进: %.2f dB)\n', ...
            pslr_ANM, pslr_DCFT, pslr_ANM - pslr_DCFT);
    
    fprintf('\n========== 运动参数估计结果 ==========\n');
    fprintf('加速度参数 α 范围: %.1f 到 %.1f\n', min(alpha_est), max(alpha_est));
    fprintf('加加速度参数 β 范围: %.1f 到 %.1f\n', min(beta_est), max(beta_est));
    fprintf('参数变化标准差 - α: %.2f, β: %.2f\n', std(alpha_est), std(beta_est));
    
end

%% 性能指标计算函数
function entropy = compute_entropy(img)
    img_norm = img / sum(img(:));
    img_norm = img_norm(img_norm > 0);
    entropy = -sum(img_norm(:) .* log(img_norm(:)));
end

function c = compute_contrast(img)
    img_norm = img / max(img(:));
    c = std(img_norm(:)) / mean(img_norm(:));
end

function pslr = compute_pslr(img, target_range)
    % 选择最强散射点
    target_img = img(target_range, :);
    [max_val, max_idx] = max(target_img(:));
    [r_max, c_max] = ind2sub(size(target_img), max_idx);
    
    % 计算峰值旁瓣比
    azimuth_profile = abs(target_img(r_max, :));
    peak_val = max(azimuth_profile);
    
    % 移除主瓣区域
    mainlobe_width = 5;  % 假设主瓣宽度
    start_idx = max(1, c_max - mainlobe_width);
    end_idx = min(length(azimuth_profile), c_max + mainlobe_width);
    
    sidelobe_profile = azimuth_profile;
    sidelobe_profile(start_idx:end_idx) = 0;
    
    max_sidelobe = max(sidelobe_profile);
    pslr = 20*log10(peak_val / (max_sidelobe + eps));
end 