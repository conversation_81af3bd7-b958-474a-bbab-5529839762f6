function [ISAR_image, motion_params, quality_metrics, convergence_info] = ANM_DCFT_ISAR_Enhanced(s_r_tm2, tm, params)
%% 基于原子范数最小化的DCFT ISAR成像增强算法
% 
% 输入参数:
%   s_r_tm2 - 距离压缩后的回波数据 [Nr x Nt]
%   tm - 时间向量
%   params - 算法参数结构体
%
% 输出参数:
%   ISAR_image - 成像结果
%   motion_params - 估计的运动参数
%   quality_metrics - 图像质量指标
%   convergence_info - 收敛信息

fprintf('\n========== ANM-DCFT ISAR成像算法 ==========\n');

%% 1. 参数初始化
if nargin < 3
    params = struct();
end

% 默认参数设置
default_params = struct(...
    'lambda', 0.1, ...           % 稀疏正则化参数
    'mu', 0.01, ...              % 平滑正则化参数
    'max_iter', 50, ...          % 最大迭代次数
    'tol', 1e-4, ...             % 收敛容差
    'L_windows', 8, ...          % 频率窗口数量
    'alpha_range', [-160, 340], ... % 二次相位参数范围
    'beta_range', [-170, 330], ...  % 三次相位参数范围
    'alpha_step', 8, ...         % 二次相位搜索步长
    'beta_step', 100, ...        % 三次相位搜索步长
    'range_cells', 30:56 ...     % 处理的距离单元
);

% 合并参数
param_names = fieldnames(default_params);
for i = 1:length(param_names)
    if ~isfield(params, param_names{i})
        params.(param_names{i}) = default_params.(param_names{i});
    end
end

%% 2. 数据预处理
[Nr, Nt] = size(s_r_tm2);
R = length(params.range_cells);

% 初始化结果矩阵
ISAR_image = zeros(Nr, Nt);
motion_params = struct('alpha', zeros(R, 1), 'beta', zeros(R, 1));

% 收敛信息
convergence_info = struct('cost', [], 'iterations', 0);

fprintf('数据维度: %d x %d\n', Nr, Nt);
fprintf('处理距离单元: %d - %d\n', params.range_cells(1), params.range_cells(end));

%% 3. 构建原子字典和频率窗口
fprintf('构建原子字典...\n');

% 频率范围
F_max = 0.5; % 归一化频率范围
freq_grid = linspace(-F_max, F_max, params.L_windows);
delta_f = 2*F_max / params.L_windows;

% 时间网格
t_grid = tm(:);
M = length(t_grid);

%% 4. 主处理循环 - 对每个距离单元进行ANM-DCFT处理
fprintf('开始ANM-DCFT处理...\n');
tic;

cost_history = [];

for r_idx = 1:R
    r = params.range_cells(r_idx);
    
    if mod(r_idx, 5) == 0
        fprintf('处理距离单元 %d/%d (第%d个)\n', r_idx, R, r);
    end
    
    % 当前距离单元的信号
    s_r = s_r_tm2(r, :).';
    
    % ANM-DCFT联合优化
    [x_r, alpha_r, beta_r, cost_r] = solve_ANM_DCFT_joint(s_r, t_grid, freq_grid, delta_f, params);
    
    % 存储结果
    ISAR_image(r, :) = abs(x_r).';
    motion_params.alpha(r_idx) = alpha_r;
    motion_params.beta(r_idx) = beta_r;
    
    % 记录代价函数
    cost_history = [cost_history; cost_r];
end

process_time = toc;
fprintf('ANM-DCFT处理完成，耗时: %.2f 秒\n', process_time);

%% 5. 后处理和质量评估
fprintf('计算图像质量指标...\n');

% 归一化处理
ISAR_image_norm = ISAR_image / max(ISAR_image(:));

% 计算质量指标
contrast_value = calculate_contrast(ISAR_image_norm);
entropy_value = calculate_entropy(ISAR_image_norm);
focus_measure = calculate_focus_measure(ISAR_image_norm);

quality_metrics = struct(...
    'contrast', contrast_value, ...
    'entropy', entropy_value, ...
    'focus_measure', focus_measure, ...
    'process_time', process_time ...
);

% 收敛信息
convergence_info.cost = cost_history;
convergence_info.iterations = length(cost_history);

%% 6. 显示结果
fprintf('\n========== 处理结果 ==========\n');
fprintf('图像对比度: %.4f\n', contrast_value);
fprintf('图像熵值: %.4f\n', entropy_value);
fprintf('聚焦度量: %.4f\n', focus_measure);
fprintf('处理时间: %.2f 秒\n', process_time);

% 显示成像结果
figure('Name', 'ANM-DCFT ISAR成像结果', 'Position', [100, 100, 800, 600]);
G1 = 20*log10(abs(ISAR_image)./max(abs(ISAR_image(:))));
imagesc(G1);
caxis([-30, 0]);
grid on;
axis xy;
colorbar;
xlabel('方位向');
ylabel('距离向');
colormap jet;
title('ANM-DCFT ISAR成像结果');

% 显示运动参数
figure('Name', '估计的运动参数', 'Position', [900, 100, 600, 400]);
subplot(2,1,1);
plot(params.range_cells, motion_params.alpha, 'b-o', 'LineWidth', 2);
grid on;
xlabel('距离单元');
ylabel('α (二次相位参数)');
title('估计的二次相位参数');

subplot(2,1,2);
plot(params.range_cells, motion_params.beta, 'r-s', 'LineWidth', 2);
grid on;
xlabel('距离单元');
ylabel('β (三次相位参数)');
title('估计的三次相位参数');

end

%% ==================== 核心算法函数 ====================

function [x_opt, alpha_opt, beta_opt, cost_final] = solve_ANM_DCFT_joint(s, t_grid, freq_grid, delta_f, params)
%% ANM-DCFT联合优化求解器

M = length(s);
L = length(freq_grid);

% 参数搜索范围
alpha_range = params.alpha_range(1):params.alpha_step:params.alpha_range(2);
beta_range = params.beta_range(1):params.beta_step:params.beta_range(2);

% 初始化
best_cost = inf;
alpha_opt = 0;
beta_opt = 0;
x_opt = zeros(M, 1);

% 网格搜索最优参数
for alpha = alpha_range
    for beta = beta_range
        % 构建观测矩阵
        A = construct_observation_matrix(t_grid, alpha, beta);
        
        % 求解原子范数最小化问题
        x_current = solve_atomic_norm_minimization(s, A, freq_grid, delta_f, params);
        
        % 计算代价函数
        cost_current = norm(s - A * x_current)^2 + params.lambda * calculate_atomic_norm_approx(x_current, freq_grid, delta_f);
        
        % 更新最优解
        if cost_current < best_cost
            best_cost = cost_current;
            alpha_opt = alpha;
            beta_opt = beta;
            x_opt = x_current;
        end
    end
end

cost_final = best_cost;

end

function A = construct_observation_matrix(t_grid, alpha, beta)
%% 构建包含高阶相位的观测矩阵

M = length(t_grid);
A = diag(exp(-1j * 2 * pi * (0.5 * alpha * t_grid.^2 + (1/6) * beta * t_grid.^3)));

end

function x = solve_atomic_norm_minimization(s, A, freq_grid, delta_f, params)
%% 求解原子范数最小化问题（简化版本）

M = length(s);

% 使用ADMM求解简化的L1问题作为原子范数的近似
% 这里使用频域稀疏性作为原子范数的近似

% 构建DFT矩阵
F = dftmtx(M) / sqrt(M);

% ADMM参数
rho = 1.0;
max_iter = 20;

% 初始化
x = zeros(M, 1);
z = zeros(M, 1);
u = zeros(M, 1);

% ADMM迭代
for iter = 1:max_iter
    % x-update
    x = (A'*A + rho*eye(M)) \ (A'*s + rho*(z - u));
    
    % z-update (软阈值)
    z_temp = F * x + u;
    z = F' * soft_threshold(z_temp, params.lambda/rho);
    
    % u-update
    u = u + F*x - z;
end

end

function norm_approx = calculate_atomic_norm_approx(x, freq_grid, delta_f)
%% 计算原子范数的近似值

% 使用L1范数作为原子范数的近似
norm_approx = norm(fft(x), 1);

end

function y = soft_threshold(x, threshold)
%% 软阈值函数

y = sign(x) .* max(abs(x) - threshold, 0);

end

%% ==================== 质量评估函数 ====================

function contrast = calculate_contrast(image)
%% 计算图像对比度

image_abs = abs(image);
mean_val = mean(image_abs(:));
std_val = std(image_abs(:));

if mean_val == 0
    contrast = 0;
else
    contrast = std_val / mean_val;
end

end

function entropy = calculate_entropy(image)
%% 计算图像熵

image_abs = abs(image);
image_norm = image_abs / sum(image_abs(:));
valid_idx = image_norm > eps;

if ~any(valid_idx)
    entropy = 0;
else
    entropy = -sum(image_norm(valid_idx) .* log2(image_norm(valid_idx)));
end

end

function focus = calculate_focus_measure(image)
%% 计算聚焦度量

image_abs = abs(image);
% 使用图像梯度的方差作为聚焦度量
[Gx, Gy] = gradient(image_abs);
focus = var(sqrt(Gx.^2 + Gy.^2), [], 'all');

end
