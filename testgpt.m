%% anm_joint_isar_full.m
% 一体化脚本：仿真舰船回波 -> DCFT+ANM 联合优化 -> 可视化聚焦结果
% 依赖：CVX（https://cvxr.com/cvx/）, 并行工具箱可选

clear; clc; close all;

%% ------------------- 1. 仿真数据生成（沿用用户提供代码） -------------------
% 目标三维点云（示例）
Pos = [
  -10 -1 0; -9 -1 0; -8 -1 0; -7 -1 0; -6 -1 0; -5 -1 0; -3.75 -1 0; -3 -1 0; -2 -1 0; -1 -1 0;
   0  -1 0;  1 -1 0;  2 -1 0;  3 -1 0;  4 -1 0;  5 -1 0;  6 -1 0;  7 -1 0;  8 -1 0;  9 -1 0;
  10 -1 0; -9.5 0.2 0.5; -9.5 1.2 0.5; -9 1 0; -8 1 0; -7 1 0; -6 1 0; -5.2 1.2 0; -4.1 1 0; -3 1 0;
  -2 1 0; -1 1 0; 0 1 0; 1 1 0; 2 1 0; 3 1 0; 4 1 0; 5 1 0; 6 1 0; 7 1 0; 8 1 0; 9 1 0; 10 1 0;
  10.5 -0.75 0; 10.5 0.75 0; 11 -0.5 0; 11 0.5 0; 11.5 0 0;
   9.5 0.5 0.5; 9.5 -0.5 0.5; 9 0 0.5; 8.5 0.5 0.5; 8.5 -0.5 0.5;
   5 0 0.5; 5 0 1; 5 0 1.5; 5 0 2; 5 0 2.5; 5 0 3; 5 0 3.5; 5 0 4;
   5.5 0.5 0.5; 5.5 -0.5 0.5; 4.5 0.5 0.5; 4.5 -0.5 0.5;
   0.5 0.5 0.9; 0.5 -0.5 0.9; -0.5 0.5 0.9; -0.5 -0.5 0.9; 0 0 0.5;
  -5 0 0.2; -5 0.2 0.8; -5 0.2 1.4; -5 0 2;
  -5.5 -0.5 0.5; -5.5 0.5 0.5; -4.4 0.5 0.5; -4.5 -0.6 0.5
];
% 仿真参数
B   = 80e6;         % 带宽
c   = 3e8;          % 光速
PRF = 1400;         % PRF
fc  = 5.2e9;        % 载频
delta_r = c/(2*B);
r = -50*delta_r:delta_r:50*delta_r;
tm = 0:1/PRF:0.501;
Num_r  = length(r);
Num_tm = length(tm);

% 运动相位参数（示例线性分配）
Num_point = size(Pos,1);
R = [cos(3*pi/8), 0, sin(3*pi/8)];  % 视角向量
% 将 Pos 投影到 R 上产生 f, alpha, beta
x_Pos = Pos(:,1)*5; y_Pos = Pos(:,2)*5; z_Pos = Pos(:,3)*5;
x_Pos = x_Pos - min(x_Pos);
y_Pos = y_Pos - min(y_Pos);
z_Pos = z_Pos - min(z_Pos);
f_    = 0.05; alpha_ = 0.05; beta_ = 0.05;
f     = (Pos*R') * f_;
alpha = (Pos*R') * alpha_;
beta  = (Pos*R') * beta_;

% 合成回波矩阵 s_r_tm2
s_r_tm2 = zeros(Num_r, Num_tm);
for k = 1:Num_point
    Delta_R0 = Pos(k,:)*R';
    tm2 = tm;
    Delta_R = Delta_R0 + 0.5*alpha(k)*tm2.^2 + (1/6)*beta(k)*tm2.^3;
    phi = 4*pi*(fc/c)*(f(k)*tm2 + 0.5*alpha(k)*tm2.^2 + (1/6)*beta(k)*tm2.^3 + Delta_R0);
    s_r_tm2 = s_r_tm2 + sinc((2*B/c)*(r.'*ones(1,Num_tm) - ones(Num_r,1)*Delta_R)) .* exp(1j*ones(Num_r,1)*phi);
end

%% ------------------- 2. 参数设置 & 算法调用 -------------------
lambda  = 0.05 * norm(s_r_tm2,'fro');
mu      = 20;
maxIter = 6;

% 调用联合 ANM-SDP ISAR 成像
img_out = anm_joint_isar(s_r_tm2, PRF, lambda, mu, maxIter);

%% ------------------- 3. 定义函数 -------------------
function img_out = anm_joint_isar(s_r_tm2, PRF, lambda, mu, maxIter)
    if nargin<5, maxIter=6; end
    [Num_r, Num_t] = size(s_r_tm2);
    t = (0:Num_t-1).' / PRF;

    % 初始化 α, β
    alpha_hat = zeros(Num_r,1);
    beta_hat  = zeros(Num_r,1);
    x_rec = zeros(Num_r, Num_t);

    for it = 1:maxIter
        fprintf('Iteration %d/%d...\n', it, maxIter);
        % 2a) ANM 稀疏恢复
        parfor r = 1:Num_r
            phase = exp(-1j*2*pi*(0.5*alpha_hat(r)*t.^2 + (1/6)*beta_hat(r)*t.^3));
            y = s_r_tm2(r,:).' .* phase;
            x_rec(r,:) = solve_anm_cvxsdp(y, lambda).';
        end
        % 2b) α,β 更新
        step = 1e-6;
        grad_a = zeros(Num_r,1);
        grad_b = zeros(Num_r,1);
        for r = 1:Num_r
            est = (x_rec(r,:).') .* exp(1j*2*pi*(0.5*alpha_hat(r)*t.^2 + (1/6)*beta_hat(r)*t.^3));
            res = s_r_tm2(r,:).' - est;
            grad_a(r) = -real(1j*2*pi*0.5*(t.^2)'*(conj(res)));
            grad_b(r) = -real(1j*2*pi*(1/6)*(t.^3)'*(conj(res)));
        end
        alpha_hat = prox_tv1d(alpha_hat - step*grad_a, mu*step);
        beta_hat  = prox_tv1d(beta_hat  - step*grad_b, mu*step);
    end

    % 3) IFFT 重建
    img_out = abs(ifftshift(ifft2(x_rec)));
    figure;
    imagesc(20*log10(img_out./max(img_out(:))));
    colormap jet; colorbar; axis xy;
    title('Joint ANM-DCFT Focused ISAR Image');
end

function x = solve_anm_cvxsdp(y, lambda)
    M = length(y);
    cvx_begin quiet sdp
        variable x(M) complex
        variable u(M) complex
        variable tau
        X = toeplitz(u);
        minimize( 0.5*sum_square_abs(y - x) + lambda*0.5*(tau+u(1)) )
        subject to
            [X, x; x', tau] == hermitian_semidefinite(M+1);
    cvx_end
end

function v = prox_tv1d(v0, w)
    N = length(v0);
    d = zeros(N-1,1);
    tol = 1e-4; maxit = 200; it = 0;
    v = v0;
    while it < maxit
        d_old = d;
        g = diff(v);
        d = max(min(d + g/4, w), -w);
        v = v0 - [d;0] + [0;d];
        if norm(d-d_old)/sqrt(N)<tol, break; end
        it = it + 1;
    end
end