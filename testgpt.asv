%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%  ISAR 成像 —— CPU 版 (Fast-SPPE + SR-DCFT + MS-PGA)
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
function R_D_DCFT = GSP_SR_DCFT_CPU(hrrp, fs, PRF, varargin)
% GSP-SR-DCFT  (CPU 版)
% INPUTS
%   hrrp : Nr × Nt complex matrix (range profiles)
%   fs   : sampling rate       [Hz]
%   PRF  : pulse-repetition freq[Hz]
% OPTIONAL
%   'FactorDop',6
%   'Lambda',0.02
% OUTPUT
%   R_D_DCFT : focused magnitude image

% ----------------- 参数 -----------------
p = inputParser;
addParameter(p,'FactorDop',6,@(x)x>0);
addParameter(p,'Lambda',0.02,@(x)x>=0);
parse(p,varargin{:});
FactorDop = p.Results.FactorDop;
lambda     = p.Results.Lambda;

% basic constants
c  = 3e8;            % light speed
fc = 5.2e9;          % carrier (adjust as needed)
[Num_r, Num_t] = size(hrrp);
t = (0:Num_t-1)/PRF; % slow-time axis

%% ---------- 1. Keystone (CPU NUFFT简化版) ----------
fprintf('Step-1 Keystone ...\n');
f_axis = linspace(-PRF/2,PRF/2,Num_t);
keystone = exp(-1j*2*pi*(fc./(fc+f_axis)).'*t).* ...
           exp(+1j*2*pi*fc.*t);
s_rcm = ifft( fft(hrrp,[],2).* keystone ,[],2);

%% ---------- 2. 多尺度 DCFT 参数估计 ----------
K = 3;                       % up to cubic
theta = zeros(1,K);          % [theta2 theta3]
dtheta0 = [8, 100];          % coarse step
rho = 0.2; Lmax = 3;         % step decay, levels
fprintf('Step-2 ParamSearch ...\n');
for lev = 1:Lmax
    dtheta = dtheta0 * rho^(lev-1);
    search_set2 = (-2:40)*dtheta(1);
    search_set3 = (-5:24)*dtheta(2);
    bestJ = Inf;
    for a = search_set2
        for b = search_set3
            comp = exp(-1j*2*pi*((1/2)*a*t.^2 + (1/6)*b*t.^3));
            tmp  = s_rcm(Num_r/2,:).*comp;
            Jval = -sum(abs(fft(tmp)).^2.*log(abs(fft(tmp)).^2+eps));
            if Jval<bestJ; bestJ=Jval; theta=[a b]; end
        end
    end
    % 牛顿微调
    theta = newton_refine(s_rcm(Num_r/2,:),t,theta,3);
end
alpha = theta(1); beta = theta(2);

%% ---------- 3. 相位补偿与 Doppler 过采样 ----------
fprintf('Step-3 Compensation ...\n');
phase_comp = exp(-1j*2*pi*((1/2)*alpha*t.^2 + (1/6)*beta*t.^3));
s_cmp = s_rcm .* phase_comp;                % Nr×Nt
Nt_os = FactorDop*Num_t;
S_fft = fft(s_cmp, Nt_os, 2);               % Nr×Nt_os

%% ---------- 4. Group-Sparse FISTA ----------
fprintf('Step-4 Sparse FISTA ...\n');
R_D_DCFT = zeros(Num_r,Nt_os);
Lips = 2;        % 估计Lipschitz常数
Kmax = 30;
for r = 1:Num_r
    y = S_fft(r,:).';
    x = zeros(Nt_os,1);
    z = x; t_k = 1;
    for k = 1:Kmax
        grad = (ifft(z) - ifft(y));
        grad = fft(grad);
        x_old = x;
        z = z - (1/Lips)*grad;
        % soft l2 shrinkage (group=单点，可自行分块)
        x = z .* max(0,1 - lambda/Lips./(abs(z)+eps));
        t_new = (1+sqrt(1+4*t_k^2))/2;
        z = x + (t_k-1)/t_new*(x - x_old);
        t_k = t_new;
    end
    R_D_DCFT(r,:) = abs(x);
    if mod(r,ceil(Num_r/10))==0
        fprintf('  %.0f%%\n',100*r/Num_r);
    end
end

%% ---------- 5. 结果可视化 ----------
figure; imagesc(20*log10(R_D_DCFT./max(R_D_DCFT(:))));
caxis([-30 0]); colormap jet; colorbar;
xlabel('Azimuth Bin'); ylabel('Range Bin');
title('GSP-SR-DCFT ISAR Image');

end

% ====== Newton refinement ======
function theta = newton_refine(s,t,theta0,Niter)
theta = theta0;
for ii = 1:Niter
    a = theta(1); b = theta(2);
    comp = exp(-1j*2*pi*((1/2)*a*t.^2 + (1/6)*b*t.^3));
    y = s.*comp; Y = fft(y);
    dJda = -2*real(sum( (j*pi*t.^2).*ifft(Y).*conj(y) ));
    dJdb = -2*real(sum( (j*(pi/3)*t.^3).*ifft(Y).*conj(y) ));
    d2J = 2*pi^2*[sum(t.^4), sum(t.^5)/3;
                  sum(t.^5)/3, sum(t.^6)/9];
    delta = d2J \ [dJda; dJdb];
    theta = theta - delta.';
    if norm(delta)<1e-3, break; end
end
end
% 主程序调用部分
load s_r_tm22.mat  % 用户自备: hrrp, fs, PRF 变量

PRF = 1400; 
% 修复函数调用参数顺序
[G_dB, M] = ISAR_DCFT_SR_CPU(s_r_tm2, PRF);

